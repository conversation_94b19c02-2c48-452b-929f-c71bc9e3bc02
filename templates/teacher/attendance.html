{% extends "base.html" %}

{% block title %}Attendance - School Management System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Attendance Management</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button class="btn btn-sm btn-primary" disabled>
                <i class="bi bi-calendar-plus me-1"></i>Take Attendance
            </button>
        </div>
    </div>
</div>

<!-- Subject Filter -->
{% if subjects %}
<div class="card mb-4">
    <div class="card-body">
        <h5 class="card-title">My Subjects</h5>
        <div class="row">
            {% for subject in subjects %}
            <div class="col-md-6 col-lg-4 mb-3">
                <div class="card border-primary">
                    <div class="card-body">
                        <h6 class="card-title">{{ subject.name }}</h6>
                        <p class="card-text">{{ subject.description or 'No description' }}</p>
                        <button class="btn btn-outline-primary btn-sm" disabled>
                            <i class="bi bi-calendar-check me-2"></i>Take Attendance
                        </button>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>
{% endif %}

<!-- Recent Attendance -->
{% if recent_attendance %}
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">Recent Attendance Records</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-sm">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Student</th>
                        <th>Subject</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    {% for record in recent_attendance %}
                    <tr>
                        <td>{{ record.date.strftime('%Y-%m-%d') }}</td>
                        <td>{{ record.student.full_name }}</td>
                        <td>{{ record.subject.name }}</td>
                        <td>
                            <span class="badge bg-{% if record.status == 'present' %}success{% elif record.status == 'absent' %}danger{% else %}warning{% endif %}">
                                {{ record.status.title() }}
                            </span>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% else %}
<div class="text-center py-5">
    <i class="bi bi-calendar-check text-muted" style="font-size: 4rem;"></i>
    <h3 class="mt-3 text-muted">No Attendance Records</h3>
    <p class="text-muted">Attendance tracking feature coming soon.</p>
</div>
{% endif %}
{% endblock %}

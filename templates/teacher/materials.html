{% extends "base.html" %}

{% block title %}Materials - School Management System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Course Materials</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('teacher.create_material') }}" class="btn btn-sm btn-primary">
                <i class="bi bi-file-plus me-1"></i>Upload Material
            </a>
        </div>
    </div>
</div>

{% if materials %}
<div class="row">
    {% for material in materials %}
    <div class="col-md-6 col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-body">
                <h5 class="card-title">{{ material.title }}</h5>
                <p class="card-text">
                    <strong>Subject:</strong> {{ material.subject.name }}<br>
                    <strong>Uploaded:</strong> {{ material.uploaded_at.strftime('%Y-%m-%d %H:%M') }}
                </p>
                
                {% if material.notes %}
                <p class="card-text">{{ material.notes[:100] }}{% if material.notes|length > 100 %}...{% endif %}</p>
                {% endif %}
                
                {% if material.file_url %}
                <a href="{{ material.file_url }}" class="btn btn-outline-primary btn-sm" target="_blank">
                    <i class="bi bi-download me-2"></i>Download
                </a>
                {% endif %}
            </div>
            <div class="card-footer text-muted">
                <small>{{ material.subject.name }}</small>
            </div>
        </div>
    </div>
    {% endfor %}
</div>
{% else %}
<div class="text-center py-5">
    <i class="bi bi-file-earmark-text text-muted" style="font-size: 4rem;"></i>
    <h3 class="mt-3 text-muted">No Materials Uploaded</h3>
    <p class="text-muted">Start by uploading your first course material.</p>
    <a href="{{ url_for('teacher.create_material') }}" class="btn btn-primary">
        <i class="bi bi-file-plus me-2"></i>Upload First Material
    </a>
</div>
{% endif %}
{% endblock %}

{% extends "base.html" %}

{% block title %}Upload Material - School Management System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Upload Course Material</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('teacher.materials') }}" class="btn btn-sm btn-outline-secondary">
                <i class="bi bi-arrow-left me-1"></i>Back to Materials
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card">
            <div class="card-body">
                <form method="POST">
                    <div class="mb-3">
                        <label for="title" class="form-label">Material Title</label>
                        <input type="text" class="form-control" id="title" name="title" required 
                               placeholder="e.g., Chapter 1 Notes, Assignment 1, etc.">
                    </div>
                    
                    <div class="mb-3">
                        <label for="subject_id" class="form-label">Subject</label>
                        <select class="form-select" id="subject_id" name="subject_id" required>
                            <option value="">Select Subject</option>
                            {% if teacher and teacher.subjects %}
                                {% for subject in teacher.subjects %}
                                <option value="{{ subject.id }}">{{ subject.name }}</option>
                                {% endfor %}
                            {% endif %}
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="file_url" class="form-label">File URL (Optional)</label>
                        <input type="url" class="form-control" id="file_url" name="file_url" 
                               placeholder="https://example.com/document.pdf">
                        <div class="form-text">Link to external file (Google Drive, Dropbox, etc.)</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">Notes/Description</label>
                        <textarea class="form-control" id="notes" name="notes" rows="4" 
                                  placeholder="Additional notes or description about this material"></textarea>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">Upload Material</button>
                        <a href="{{ url_for('teacher.materials') }}" class="btn btn-outline-secondary">Cancel</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% extends "base.html" %}

{% block title %}Tests - School Management System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Tests & Assessments</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('teacher.create_test') }}" class="btn btn-sm btn-primary">
                <i class="bi bi-clipboard-plus me-1"></i>Create Test
            </a>
        </div>
    </div>
</div>

{% if tests %}
<div class="row">
    {% for test in tests %}
    <div class="col-md-6 col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-body">
                <h5 class="card-title">{{ test.title }}</h5>
                <p class="card-text">
                    <strong>Subject:</strong> {{ test.subject.name }}<br>
                    <strong>Class:</strong> {{ test.class_.name }}<br>
                    <strong>Date:</strong> {{ test.date.strftime('%Y-%m-%d') }}<br>
                    <strong>Total Score:</strong> {{ test.total_score }}
                </p>
                
                <div class="d-grid gap-2">
                    <a href="{{ url_for('teacher.grade_test', test_id=test.id) }}" class="btn btn-outline-primary btn-sm">
                        <i class="bi bi-pencil-square me-2"></i>Grade Test
                    </a>
                    <button class="btn btn-outline-info btn-sm" disabled>
                        <i class="bi bi-bar-chart me-2"></i>View Results
                    </button>
                </div>
            </div>
            <div class="card-footer text-muted">
                <small>{{ test.results|length }} students graded</small>
            </div>
        </div>
    </div>
    {% endfor %}
</div>
{% else %}
<div class="text-center py-5">
    <i class="bi bi-clipboard-check text-muted" style="font-size: 4rem;"></i>
    <h3 class="mt-3 text-muted">No Tests Created</h3>
    <p class="text-muted">Start by creating your first test or assessment.</p>
    <a href="{{ url_for('teacher.create_test') }}" class="btn btn-primary">
        <i class="bi bi-clipboard-plus me-2"></i>Create First Test
    </a>
</div>
{% endif %}
{% endblock %}

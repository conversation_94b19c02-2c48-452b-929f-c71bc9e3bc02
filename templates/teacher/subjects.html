{% extends "base.html" %}

{% block title %}My Subjects - School Management System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">My Subjects</h1>
</div>

{% if subjects %}
<div class="row">
    {% for subject in subjects %}
    <div class="col-md-6 col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-body">
                <h5 class="card-title">{{ subject.name }}</h5>
                <p class="card-text">{{ subject.description or 'No description available' }}</p>
                
                <div class="d-grid gap-2">
                    <a href="{{ url_for('teacher.materials') }}?subject={{ subject.id }}" class="btn btn-outline-primary btn-sm">
                        <i class="bi bi-file-earmark-text me-2"></i>Materials
                    </a>
                    <a href="{{ url_for('teacher.tests') }}?subject={{ subject.id }}" class="btn btn-outline-success btn-sm">
                        <i class="bi bi-clipboard-check me-2"></i>Tests
                    </a>
                    <a href="{{ url_for('teacher.attendance') }}?subject={{ subject.id }}" class="btn btn-outline-info btn-sm">
                        <i class="bi bi-calendar-check me-2"></i>Attendance
                    </a>
                </div>
            </div>
            <div class="card-footer text-muted">
                <small>Subject ID: {{ subject.id }}</small>
            </div>
        </div>
    </div>
    {% endfor %}
</div>
{% else %}
<div class="text-center py-5">
    <i class="bi bi-book text-muted" style="font-size: 4rem;"></i>
    <h3 class="mt-3 text-muted">No Subjects Assigned</h3>
    <p class="text-muted">Contact your administrator to get subjects assigned to you.</p>
</div>
{% endif %}
{% endblock %}

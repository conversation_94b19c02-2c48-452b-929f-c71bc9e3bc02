<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}School Management System{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        .navbar-brand {
            font-weight: 600;
        }
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .btn {
            border-radius: 0.375rem;
        }
        .sidebar {
            min-height: calc(100vh - 56px);
            background-color: #f8f9fa;
        }
        .main-content {
            min-height: calc(100vh - 56px);
        }
        @media (max-width: 768px) {
            .sidebar {
                min-height: auto;
            }
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="bi bi-mortarboard-fill me-2"></i>School Management
            </a>
            
            {% if current_user.is_authenticated %}
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    {% if current_user.role == 'admin' %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('admin.dashboard') }}">Dashboard</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            Manage
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('admin.users') }}">Users</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('admin.students') }}">Students</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('admin.classes') }}">Classes</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('admin.subjects') }}">Subjects</a></li>
                        </ul>
                    </li>
                    {% elif current_user.role == 'teacher' %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('teacher.dashboard') }}">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('teacher.subjects') }}">My Subjects</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('teacher.materials') }}">Materials</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('teacher.tests') }}">Tests</a>
                    </li>
                    {% elif current_user.role == 'parent' %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('parent.dashboard') }}">Dashboard</a>
                    </li>
                    {% endif %}
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle me-1"></i>{{ current_user.full_name }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><span class="dropdown-item-text small text-muted">{{ current_user.role.title() }}</span></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">
                                <i class="bi bi-box-arrow-right me-2"></i>Logout
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
            {% endif %}
        </div>
    </nav>

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="container-fluid mt-3">
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else 'success' if category == 'success' else 'info' }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}

    <!-- Main Content -->
    <div class="container-fluid">
        <div class="row">
            {% if current_user.is_authenticated and current_user.role in ['admin', 'teacher'] %}
            <!-- Sidebar for admin and teacher -->
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    {% block sidebar %}
                    {% if current_user.role == 'admin' %}
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('admin.dashboard') }}">
                                <i class="bi bi-speedometer2 me-2"></i>Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('admin.users') }}">
                                <i class="bi bi-people me-2"></i>Users
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('admin.students') }}">
                                <i class="bi bi-person-badge me-2"></i>Students
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('admin.classes') }}">
                                <i class="bi bi-collection me-2"></i>Classes
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('admin.subjects') }}">
                                <i class="bi bi-book me-2"></i>Subjects
                            </a>
                        </li>
                    </ul>
                    {% elif current_user.role == 'teacher' %}
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('teacher.dashboard') }}">
                                <i class="bi bi-speedometer2 me-2"></i>Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('teacher.subjects') }}">
                                <i class="bi bi-book me-2"></i>My Subjects
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('teacher.materials') }}">
                                <i class="bi bi-file-earmark-text me-2"></i>Materials
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('teacher.tests') }}">
                                <i class="bi bi-clipboard-check me-2"></i>Tests
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('teacher.attendance') }}">
                                <i class="bi bi-calendar-check me-2"></i>Attendance
                            </a>
                        </li>
                    </ul>
                    {% endif %}
                    {% endblock %}
                </div>
            </div>

            <!-- Main content area with sidebar -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
            {% else %}
            <!-- Full width for non-authenticated users and parents -->
            <main class="col-12 px-md-4 main-content">
            {% endif %}
                {% block content %}{% endblock %}
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>

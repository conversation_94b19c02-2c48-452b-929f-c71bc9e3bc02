{% extends "base.html" %}

{% block title %}Create Subject - School Management System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Add New Subject</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('admin.subjects') }}" class="btn btn-sm btn-outline-secondary">
                <i class="bi bi-arrow-left me-1"></i>Back to Subjects
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card">
            <div class="card-body">
                <form method="POST">
                    <div class="mb-3">
                        <label for="name" class="form-label">Subject Name</label>
                        <input type="text" class="form-control" id="name" name="name" required 
                               placeholder="e.g., Mathematics, English, Science">
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3" 
                                  placeholder="Brief description of the subject (optional)"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="teacher_id" class="form-label">Assign Teacher (Optional)</label>
                        <select class="form-select" id="teacher_id" name="teacher_id">
                            <option value="">No teacher assigned</option>
                            {% for teacher in teachers %}
                            <option value="{{ teacher.id }}">
                                {{ teacher.user.full_name }}
                                {% if teacher.staff_id %} ({{ teacher.staff_id }}){% endif %}
                            </option>
                            {% endfor %}
                        </select>
                        <div class="form-text">You can assign a teacher later if needed</div>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">Add Subject</button>
                        <a href="{{ url_for('admin.subjects') }}" class="btn btn-outline-secondary">Cancel</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% extends "base.html" %}

{% block title %}Users - School Management System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Users</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('auth.register') }}" class="btn btn-sm btn-primary">
                <i class="bi bi-person-plus me-1"></i>Add User
            </a>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-body">
        {% if users.items %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Email</th>
                        <th>Role</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in users.items %}
                    <tr>
                        <td>{{ user.full_name }}</td>
                        <td>{{ user.email }}</td>
                        <td>
                            <span class="badge bg-{% if user.role == 'admin' %}danger{% elif user.role == 'teacher' %}primary{% elif user.role == 'parent' %}success{% else %}secondary{% endif %}">
                                {{ user.role.title() }}
                            </span>
                        </td>
                        <td>{{ user.created_at.strftime('%Y-%m-%d') }}</td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary btn-sm" disabled>
                                    <i class="bi bi-pencil"></i>
                                </button>
                                <button class="btn btn-outline-danger btn-sm" disabled>
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if users.pages > 1 %}
        <nav aria-label="Users pagination">
            <ul class="pagination justify-content-center">
                {% if users.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('admin.users', page=users.prev_num) }}">Previous</a>
                </li>
                {% endif %}
                
                {% for page_num in users.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != users.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('admin.users', page=page_num) }}">{{ page_num }}</a>
                        </li>
                        {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% endif %}
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">...</span>
                    </li>
                    {% endif %}
                {% endfor %}
                
                {% if users.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('admin.users', page=users.next_num) }}">Next</a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
        
        {% else %}
        <div class="text-center py-5">
            <i class="bi bi-people text-muted" style="font-size: 3rem;"></i>
            <h4 class="mt-3 text-muted">No Users Found</h4>
            <p class="text-muted">Start by creating the first user account.</p>
            <a href="{{ url_for('auth.register') }}" class="btn btn-primary">
                <i class="bi bi-person-plus me-2"></i>Add First User
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

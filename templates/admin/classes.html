{% extends "base.html" %}

{% block title %}Classes - School Management System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Classes</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('admin.create_class') }}" class="btn btn-sm btn-primary">
                <i class="bi bi-plus-circle me-1"></i>Create Class
            </a>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-body">
        {% if classes %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Class Name</th>
                        <th>Academic Year</th>
                        <th>Students Enrolled</th>
                        <th>Subjects</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for class in classes %}
                    <tr>
                        <td>{{ class.name }}</td>
                        <td>{{ class.academic_year }}</td>
                        <td>
                            <span class="badge bg-info">{{ class.enrollments|length }} students</span>
                        </td>
                        <td>
                            <span class="badge bg-secondary">{{ class.subjects|length }} subjects</span>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary btn-sm" disabled>
                                    <i class="bi bi-pencil"></i>
                                </button>
                                <button class="btn btn-outline-info btn-sm" disabled>
                                    <i class="bi bi-eye"></i>
                                </button>
                                <button class="btn btn-outline-danger btn-sm" disabled>
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="bi bi-collection text-muted" style="font-size: 3rem;"></i>
            <h4 class="mt-3 text-muted">No Classes Found</h4>
            <p class="text-muted">Start by creating the first class.</p>
            <a href="{{ url_for('admin.create_class') }}" class="btn btn-primary">
                <i class="bi bi-plus-circle me-2"></i>Create First Class
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

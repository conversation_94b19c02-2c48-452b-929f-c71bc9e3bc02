{% extends "base.html" %}

{% block title %}Subjects - School Management System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Subjects</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('admin.create_subject') }}" class="btn btn-sm btn-primary">
                <i class="bi bi-book-fill me-1"></i>Add Subject
            </a>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-body">
        {% if subjects %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Subject Name</th>
                        <th>Description</th>
                        <th>Teacher</th>
                        <th>Classes</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for subject in subjects %}
                    <tr>
                        <td>{{ subject.name }}</td>
                        <td>{{ subject.description or 'No description' }}</td>
                        <td>
                            {% if subject.teacher %}
                                {{ subject.teacher.user.full_name }}
                                <br><small class="text-muted">{{ subject.teacher.staff_id or 'No staff ID' }}</small>
                            {% else %}
                                <span class="text-muted">No teacher assigned</span>
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge bg-info">{{ subject.classes|length }} classes</span>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary btn-sm" disabled>
                                    <i class="bi bi-pencil"></i>
                                </button>
                                <button class="btn btn-outline-danger btn-sm" disabled>
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="bi bi-book text-muted" style="font-size: 3rem;"></i>
            <h4 class="mt-3 text-muted">No Subjects Found</h4>
            <p class="text-muted">Start by creating the first subject.</p>
            <a href="{{ url_for('admin.create_subject') }}" class="btn btn-primary">
                <i class="bi bi-book-fill me-2"></i>Add First Subject
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

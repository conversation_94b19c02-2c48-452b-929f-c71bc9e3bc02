{% extends "base.html" %}

{% block title %}Students - School Management System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Students</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('admin.create_student') }}" class="btn btn-sm btn-primary">
                <i class="bi bi-person-plus me-1"></i>Add Student
            </a>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-body">
        {% if students.items %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Date of Birth</th>
                        <th>Gender</th>
                        <th>Parent</th>
                        <th>Registration Date</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for student in students.items %}
                    <tr>
                        <td>{{ student.full_name }}</td>
                        <td>{{ student.dob.strftime('%Y-%m-%d') if student.dob else 'N/A' }}</td>
                        <td>{{ student.gender or 'N/A' }}</td>
                        <td>
                            {% if student.parent %}
                                {{ student.parent.full_name }}
                                <br><small class="text-muted">{{ student.parent.email }}</small>
                            {% else %}
                                <span class="text-muted">No parent assigned</span>
                            {% endif %}
                        </td>
                        <td>{{ student.registration_date.strftime('%Y-%m-%d') }}</td>
                        <td>
                            {% if student.is_active %}
                                <span class="badge bg-success">Active</span>
                            {% else %}
                                <span class="badge bg-secondary">Inactive</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary btn-sm" disabled>
                                    <i class="bi bi-pencil"></i>
                                </button>
                                <button class="btn btn-outline-danger btn-sm" disabled>
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if students.pages > 1 %}
        <nav aria-label="Students pagination">
            <ul class="pagination justify-content-center">
                {% if students.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('admin.students', page=students.prev_num) }}">Previous</a>
                </li>
                {% endif %}
                
                {% for page_num in students.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != students.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('admin.students', page=page_num) }}">{{ page_num }}</a>
                        </li>
                        {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% endif %}
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">...</span>
                    </li>
                    {% endif %}
                {% endfor %}
                
                {% if students.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('admin.students', page=students.next_num) }}">Next</a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
        
        {% else %}
        <div class="text-center py-5">
            <i class="bi bi-person-badge text-muted" style="font-size: 3rem;"></i>
            <h4 class="mt-3 text-muted">No Students Found</h4>
            <p class="text-muted">Start by registering the first student.</p>
            <a href="{{ url_for('admin.create_student') }}" class="btn btn-primary">
                <i class="bi bi-person-plus me-2"></i>Add First Student
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

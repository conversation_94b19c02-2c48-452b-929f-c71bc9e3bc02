from flask_login import UserMixin
from datetime import datetime
import bcrypt
import json

# Import db from database module
from database import db

class User(UserMixin, db.Model):
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    full_name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(128), nullable=False)
    role = db.Column(db.String(20), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    teacher = db.relationship('Teacher', backref='user', uselist=False)
    children = db.relationship('Student', backref='parent', foreign_keys='Student.parent_id')
    
    __table_args__ = (
        db.CheckConstraint("role IN ('admin', 'teacher', 'parent', 'student')", name='check_role'),
    )
    
    def set_password(self, password):
        """Hash and set password"""
        self.password_hash = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
    
    def check_password(self, password):
        """Check if provided password matches hash"""
        return bcrypt.checkpw(password.encode('utf-8'), self.password_hash.encode('utf-8'))

class Student(db.Model):
    __tablename__ = 'students'
    
    id = db.Column(db.Integer, primary_key=True)
    full_name = db.Column(db.String(100), nullable=False)
    dob = db.Column(db.Date)
    gender = db.Column(db.String(10))
    registration_date = db.Column(db.DateTime, default=datetime.utcnow)
    parent_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    is_active = db.Column(db.Boolean, default=True)
    
    # Relationships
    enrollments = db.relationship('ClassEnrollment', backref='student')
    attendance_records = db.relationship('Attendance', backref='student')
    test_results = db.relationship('TestResult', backref='student')
    reports = db.relationship('StudentReport', backref='student')

class Teacher(db.Model):
    __tablename__ = 'teachers'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), unique=True)
    staff_id = db.Column(db.String(20), unique=True)
    hire_date = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    subjects = db.relationship('Subject', backref='teacher')
    materials = db.relationship('Material', backref='teacher')
    reports = db.relationship('StudentReport', backref='teacher')

class Class(db.Model):
    __tablename__ = 'classes'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False)
    academic_year = db.Column(db.String(20))
    
    # Relationships
    enrollments = db.relationship('ClassEnrollment', backref='class_')
    subjects = db.relationship('SubjectClass', backref='class_')
    tests = db.relationship('Test', backref='class_')
    attendance_records = db.relationship('Attendance', backref='class_')
    reports = db.relationship('StudentReport', backref='class_')

class Subject(db.Model):
    __tablename__ = 'subjects'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    teacher_id = db.Column(db.Integer, db.ForeignKey('teachers.id'))
    
    # Relationships
    classes = db.relationship('SubjectClass', backref='subject')
    materials = db.relationship('Material', backref='subject')
    tests = db.relationship('Test', backref='subject')
    attendance_records = db.relationship('Attendance', backref='subject')
    reports = db.relationship('StudentReport', backref='subject')

class ClassEnrollment(db.Model):
    __tablename__ = 'class_enrollments'
    
    id = db.Column(db.Integer, primary_key=True)
    student_id = db.Column(db.Integer, db.ForeignKey('students.id'))
    class_id = db.Column(db.Integer, db.ForeignKey('classes.id'))
    enrolled_on = db.Column(db.DateTime, default=datetime.utcnow)

class SubjectClass(db.Model):
    __tablename__ = 'subject_classes'
    
    id = db.Column(db.Integer, primary_key=True)
    class_id = db.Column(db.Integer, db.ForeignKey('classes.id'))
    subject_id = db.Column(db.Integer, db.ForeignKey('subjects.id'))

class Attendance(db.Model):
    __tablename__ = 'attendance'
    
    id = db.Column(db.Integer, primary_key=True)
    student_id = db.Column(db.Integer, db.ForeignKey('students.id'))
    subject_id = db.Column(db.Integer, db.ForeignKey('subjects.id'))
    class_id = db.Column(db.Integer, db.ForeignKey('classes.id'))
    date = db.Column(db.Date, nullable=False)
    status = db.Column(db.String(20), nullable=False)
    
    __table_args__ = (
        db.CheckConstraint("status IN ('present', 'absent', 'excused')", name='check_attendance_status'),
    )

class Material(db.Model):
    __tablename__ = 'materials'
    
    id = db.Column(db.Integer, primary_key=True)
    subject_id = db.Column(db.Integer, db.ForeignKey('subjects.id'))
    teacher_id = db.Column(db.Integer, db.ForeignKey('teachers.id'))
    title = db.Column(db.String(200), nullable=False)
    file_url = db.Column(db.String(500))
    notes = db.Column(db.Text)
    uploaded_at = db.Column(db.DateTime, default=datetime.utcnow)

class Test(db.Model):
    __tablename__ = 'tests'
    
    id = db.Column(db.Integer, primary_key=True)
    subject_id = db.Column(db.Integer, db.ForeignKey('subjects.id'))
    class_id = db.Column(db.Integer, db.ForeignKey('classes.id'))
    title = db.Column(db.String(200), nullable=False)
    date = db.Column(db.Date, nullable=False)
    total_score = db.Column(db.Integer, nullable=False)
    grade_scale = db.Column(db.Text)  # JSON string
    
    # Relationships
    results = db.relationship('TestResult', backref='test')
    
    def set_grade_scale(self, scale_dict):
        """Set grade scale as JSON string"""
        self.grade_scale = json.dumps(scale_dict)
    
    def get_grade_scale(self):
        """Get grade scale as dictionary"""
        if self.grade_scale:
            return json.loads(self.grade_scale)
        return {}
    
    def calculate_grade(self, score):
        """Calculate grade based on score and grade scale"""
        scale = self.get_grade_scale()
        for grade, min_score in sorted(scale.items(), key=lambda x: x[1], reverse=True):
            if score >= min_score:
                return grade
        return 'F'

class TestResult(db.Model):
    __tablename__ = 'test_results'
    
    id = db.Column(db.Integer, primary_key=True)
    test_id = db.Column(db.Integer, db.ForeignKey('tests.id'))
    student_id = db.Column(db.Integer, db.ForeignKey('students.id'))
    score = db.Column(db.Integer, nullable=False)
    grade = db.Column(db.String(5))

class StudentReport(db.Model):
    __tablename__ = 'student_reports'
    
    id = db.Column(db.Integer, primary_key=True)
    student_id = db.Column(db.Integer, db.ForeignKey('students.id'))
    subject_id = db.Column(db.Integer, db.ForeignKey('subjects.id'))
    class_id = db.Column(db.Integer, db.ForeignKey('classes.id'))
    teacher_id = db.Column(db.Integer, db.ForeignKey('teachers.id'))
    report_text = db.Column(db.Text, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

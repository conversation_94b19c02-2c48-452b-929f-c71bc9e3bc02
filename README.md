# School Management Web Portal

A mobile-first web application built with Flask, SQLAlchemy, and Bootstrap for managing students, teachers, classes, subjects, tests, and parent interactions.

## Features

### 🔐 User Roles & Authentication
- **Admin**: Manages school configuration, user accounts, classes, and academic structure
- **Teacher**: Manages subjects, uploads materials, creates tests, tracks attendance, and writes reports
- **Parent**: Views student performance, attendance, and reports for their children
- **Student**: Basic portal to view grades and materials (optional)

### 📚 Core Functionality
- User management with role-based access control
- Student registration and management
- Class and subject management
- Course material upload and sharing
- Test creation and grading system
- Attendance tracking
- Student performance reports
- Parent portal for monitoring children's progress

## Technology Stack

- **Backend**: Flask (Python)
- **Database**: SQLite3 with SQLAlchemy ORM
- **Frontend**: Bootstrap 5 (minimalistic and flat design)
- **Authentication**: Flask-Login with bcrypt password hashing
- **Styling**: Mobile-first responsive design

## Installation & Setup

### Prerequisites
- Python 3.8+
- pip (Python package manager)

### Installation Steps

1. **Clone or download the project**
   ```bash
   cd /path/to/project
   ```

2. **Create virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Configure environment variables**
   - Copy `.env` file and update settings if needed
   - Default configuration uses SQLite database

5. **Run the application**
   ```bash
   python app.py
   ```

6. **Access the application**
   - Open browser and go to `http://localhost:5000`
   - The application will redirect to the registration page for first-time setup

## First-Time Setup

### 1. Create Admin Account
- Visit `http://localhost:5000/auth/register`
- Create the first user (automatically becomes admin)
- Example credentials:
  - Name: `Admin User`
  - Email: `<EMAIL>`
  - Password: `admin123`

### 2. Set Up School Data
After logging in as admin:
1. **Create Classes**: Admin Dashboard → Create Class
2. **Add Subjects**: Admin Dashboard → Add Subject
3. **Create Teacher Accounts**: Admin Dashboard → Add User (Role: Teacher)
4. **Create Parent Accounts**: Admin Dashboard → Add User (Role: Parent)
5. **Register Students**: Admin Dashboard → Add Student

## Usage Guide

### Admin Functions
- **User Management**: Create and manage user accounts
- **Student Management**: Register students and assign to parents
- **Class Management**: Create and organize classes
- **Subject Management**: Create subjects and assign teachers
- **System Overview**: Dashboard with statistics and recent activity

### Teacher Functions
- **Subject Management**: View assigned subjects
- **Material Upload**: Share course materials and resources
- **Test Creation**: Create tests with custom grading scales
- **Grade Management**: Grade student tests and assignments
- **Attendance Tracking**: Record student attendance (basic implementation)

### Parent Functions
- **Child Overview**: View all registered children
- **Performance Monitoring**: Track test results and grades
- **Attendance Reports**: View attendance records
- **Teacher Reports**: Read subjective performance reports

## Database Schema

The application uses 12 main tables:
- `users` - User accounts and authentication
- `students` - Student information and registration
- `teachers` - Teacher profiles and staff details
- `classes` - Class organization
- `subjects` - Subject definitions
- `class_enrollments` - Student-class relationships
- `subject_classes` - Subject-class relationships
- `attendance` - Attendance records
- `materials` - Course materials and resources
- `tests` - Test definitions and grading scales
- `test_results` - Individual test scores and grades
- `student_reports` - Teacher-written performance reports

## Security Features

- **Password Hashing**: bcrypt for secure password storage
- **Role-Based Access**: Decorators ensure proper authorization
- **Session Management**: Flask-Login for secure sessions
- **Input Validation**: Form validation and sanitization

## Mobile-First Design

- Responsive Bootstrap layout
- Touch-friendly interface
- Optimized for mobile devices
- Progressive enhancement for desktop

## Development Notes

### Project Structure
```
├── app.py              # Main application file
├── database.py         # Database initialization
├── models.py           # SQLAlchemy models
├── utils.py            # Utility functions and decorators
├── routes/             # Route blueprints
│   ├── auth.py         # Authentication routes
│   ├── admin.py        # Admin panel routes
│   ├── teacher.py      # Teacher portal routes
│   ├── parent.py       # Parent portal routes
│   └── student.py      # Student portal routes
├── templates/          # Jinja2 templates
│   ├── base.html       # Base template
│   ├── auth/           # Authentication templates
│   ├── admin/          # Admin panel templates
│   ├── teacher/        # Teacher portal templates
│   ├── parent/         # Parent portal templates
│   └── student/        # Student portal templates
├── static/             # Static files (CSS, JS)
├── requirements.txt    # Python dependencies
└── .env               # Environment configuration
```

### Key Design Decisions
- **SQLite**: Simple deployment and setup
- **Blueprint Architecture**: Modular route organization
- **Role-Based Decorators**: Clean authorization system
- **Mobile-First**: Bootstrap responsive design
- **Minimal Dependencies**: Focus on core functionality

## Future Enhancements

- PDF report card generation
- Bulk import/export functionality
- Email notifications
- Advanced attendance features
- Student messaging system
- Grade analytics and charts
- File upload for materials
- Calendar integration

## Support

For issues or questions:
1. Check the application logs
2. Verify database connectivity
3. Ensure all dependencies are installed
4. Check browser console for frontend errors

## License

This project is built for educational and demonstration purposes.

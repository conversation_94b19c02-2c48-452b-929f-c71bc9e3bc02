from flask import Blueprint, render_template, request, redirect, url_for, flash
from flask_login import login_user, logout_user, login_required, current_user
from models import User
from database import db

auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('index'))
    
    if request.method == 'POST':
        email = request.form.get('email')
        password = request.form.get('password')
        
        if not email or not password:
            flash('Please fill in all fields', 'error')
            return render_template('auth/login.html')
        
        user = User.query.filter_by(email=email).first()
        
        if user and user.check_password(password):
            login_user(user)
            next_page = request.args.get('next')
            if next_page:
                return redirect(next_page)
            return redirect(url_for('index'))
        else:
            flash('Invalid email or password', 'error')
    
    return render_template('auth/login.html')

@auth_bp.route('/logout')
@login_required
def logout():
    logout_user()
    flash('You have been logged out successfully', 'success')
    return redirect(url_for('auth.login'))

@auth_bp.route('/register', methods=['GET', 'POST'])
def register():
    # Only allow registration if user is admin or no users exist
    if current_user.is_authenticated and current_user.role != 'admin':
        flash('Only administrators can register new users', 'error')
        return redirect(url_for('index'))
    
    # Check if this is the first user (admin setup)
    user_count = User.query.count()
    is_first_user = user_count == 0
    
    if request.method == 'POST':
        full_name = request.form.get('full_name')
        email = request.form.get('email')
        password = request.form.get('password')
        confirm_password = request.form.get('confirm_password')
        role = request.form.get('role', 'admin' if is_first_user else 'parent')
        
        # Validation
        if not all([full_name, email, password, confirm_password]):
            flash('Please fill in all fields', 'error')
            return render_template('auth/register.html', is_first_user=is_first_user)
        
        if password != confirm_password:
            flash('Passwords do not match', 'error')
            return render_template('auth/register.html', is_first_user=is_first_user)
        
        if len(password) < 6:
            flash('Password must be at least 6 characters long', 'error')
            return render_template('auth/register.html', is_first_user=is_first_user)
        
        # Check if email already exists
        if User.query.filter_by(email=email).first():
            flash('Email already registered', 'error')
            return render_template('auth/register.html', is_first_user=is_first_user)
        
        # Create new user
        user = User(
            full_name=full_name,
            email=email,
            role=role
        )
        user.set_password(password)
        
        try:
            db.session.add(user)
            db.session.commit()
            
            if is_first_user:
                login_user(user)
                flash('Admin account created successfully! Welcome to the School Management System.', 'success')
                return redirect(url_for('index'))
            else:
                flash('User registered successfully', 'success')
                return redirect(url_for('auth.register'))
                
        except Exception as e:
            db.session.rollback()
            flash('An error occurred during registration. Please try again.', 'error')
    
    return render_template('auth/register.html', is_first_user=is_first_user)

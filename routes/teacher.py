from flask import Blueprint, render_template, request, redirect, url_for, flash
from flask_login import login_required, current_user
from models import Teacher, Subject, Class, Student, Material, Test, TestResult, Attendance, StudentReport
from database import db
from utils import teacher_required, teacher_or_admin_required
from datetime import datetime, date

teacher_bp = Blueprint('teacher', __name__)

@teacher_bp.route('/dashboard')
@login_required
@teacher_required
def dashboard():
    # Get teacher record
    teacher = Teacher.query.filter_by(user_id=current_user.id).first()
    if not teacher:
        flash('Teacher profile not found', 'error')
        return redirect(url_for('index'))
    
    # Get teacher's subjects
    subjects = Subject.query.filter_by(teacher_id=teacher.id).all()
    
    # Get recent materials and tests
    recent_materials = Material.query.filter_by(teacher_id=teacher.id).order_by(Material.uploaded_at.desc()).limit(5).all()
    recent_tests = Test.query.join(Subject).filter(Subject.teacher_id == teacher.id).order_by(Test.date.desc()).limit(5).all()
    
    return render_template('teacher/dashboard.html', 
                         teacher=teacher,
                         subjects=subjects,
                         recent_materials=recent_materials,
                         recent_tests=recent_tests)

@teacher_bp.route('/subjects')
@login_required
@teacher_required
def subjects():
    teacher = Teacher.query.filter_by(user_id=current_user.id).first()
    subjects = Subject.query.filter_by(teacher_id=teacher.id).all()
    return render_template('teacher/subjects.html', subjects=subjects)

@teacher_bp.route('/materials')
@login_required
@teacher_required
def materials():
    teacher = Teacher.query.filter_by(user_id=current_user.id).first()
    materials = Material.query.filter_by(teacher_id=teacher.id).order_by(Material.uploaded_at.desc()).all()
    return render_template('teacher/materials.html', materials=materials)

@teacher_bp.route('/materials/create', methods=['GET', 'POST'])
@login_required
@teacher_required
def create_material():
    teacher = Teacher.query.filter_by(user_id=current_user.id).first()
    
    if request.method == 'POST':
        title = request.form.get('title')
        subject_id = request.form.get('subject_id')
        file_url = request.form.get('file_url')
        notes = request.form.get('notes')
        
        if not all([title, subject_id]):
            flash('Title and subject are required', 'error')
            return render_template('teacher/create_material.html', teacher=teacher)
        
        # Verify subject belongs to teacher
        subject = Subject.query.filter_by(id=subject_id, teacher_id=teacher.id).first()
        if not subject:
            flash('Invalid subject selected', 'error')
            return render_template('teacher/create_material.html', teacher=teacher)
        
        material = Material(
            subject_id=subject_id,
            teacher_id=teacher.id,
            title=title,
            file_url=file_url,
            notes=notes
        )
        
        try:
            db.session.add(material)
            db.session.commit()
            flash('Material uploaded successfully', 'success')
            return redirect(url_for('teacher.materials'))
        except Exception as e:
            db.session.rollback()
            flash('Error uploading material', 'error')
    
    return render_template('teacher/create_material.html', teacher=teacher)

@teacher_bp.route('/tests')
@login_required
@teacher_required
def tests():
    teacher = Teacher.query.filter_by(user_id=current_user.id).first()
    tests = Test.query.join(Subject).filter(Subject.teacher_id == teacher.id).order_by(Test.date.desc()).all()
    return render_template('teacher/tests.html', tests=tests)

@teacher_bp.route('/tests/create', methods=['GET', 'POST'])
@login_required
@teacher_required
def create_test():
    teacher = Teacher.query.filter_by(user_id=current_user.id).first()
    
    if request.method == 'POST':
        title = request.form.get('title')
        subject_id = request.form.get('subject_id')
        class_id = request.form.get('class_id')
        test_date = request.form.get('date')
        total_score = request.form.get('total_score')
        
        # Grade scale
        grade_a = request.form.get('grade_a', 90)
        grade_b = request.form.get('grade_b', 80)
        grade_c = request.form.get('grade_c', 70)
        grade_d = request.form.get('grade_d', 60)
        grade_e = request.form.get('grade_e', 50)
        
        if not all([title, subject_id, class_id, test_date, total_score]):
            flash('All fields are required', 'error')
            return render_template('teacher/create_test.html', teacher=teacher)
        
        # Verify subject belongs to teacher
        subject = Subject.query.filter_by(id=subject_id, teacher_id=teacher.id).first()
        if not subject:
            flash('Invalid subject selected', 'error')
            return render_template('teacher/create_test.html', teacher=teacher)
        
        try:
            test_date = datetime.strptime(test_date, '%Y-%m-%d').date()
            total_score = int(total_score)
            
            grade_scale = {
                'A': int(grade_a),
                'B': int(grade_b),
                'C': int(grade_c),
                'D': int(grade_d),
                'E': int(grade_e),
                'F': 0
            }
            
            test = Test(
                subject_id=subject_id,
                class_id=class_id,
                title=title,
                date=test_date,
                total_score=total_score
            )
            test.set_grade_scale(grade_scale)
            
            db.session.add(test)
            db.session.commit()
            flash('Test created successfully', 'success')
            return redirect(url_for('teacher.tests'))
            
        except ValueError:
            flash('Invalid date or score format', 'error')
        except Exception as e:
            db.session.rollback()
            flash('Error creating test', 'error')
    
    return render_template('teacher/create_test.html', teacher=teacher)

@teacher_bp.route('/tests/<int:test_id>/grade', methods=['GET', 'POST'])
@login_required
@teacher_required
def grade_test(test_id):
    teacher = Teacher.query.filter_by(user_id=current_user.id).first()
    test = Test.query.join(Subject).filter(Test.id == test_id, Subject.teacher_id == teacher.id).first()
    
    if not test:
        flash('Test not found', 'error')
        return redirect(url_for('teacher.tests'))
    
    if request.method == 'POST':
        # Process grades
        for student_id in request.form.getlist('student_ids'):
            score = request.form.get(f'score_{student_id}')
            if score:
                try:
                    score = int(score)
                    grade = test.calculate_grade(score)
                    
                    # Check if result already exists
                    existing_result = TestResult.query.filter_by(test_id=test_id, student_id=student_id).first()
                    if existing_result:
                        existing_result.score = score
                        existing_result.grade = grade
                    else:
                        result = TestResult(test_id=test_id, student_id=student_id, score=score, grade=grade)
                        db.session.add(result)
                        
                except ValueError:
                    continue
        
        try:
            db.session.commit()
            flash('Grades saved successfully', 'success')
            return redirect(url_for('teacher.tests'))
        except Exception as e:
            db.session.rollback()
            flash('Error saving grades', 'error')
    
    # Get students enrolled in the test's class
    from models import ClassEnrollment
    students = db.session.query(Student).join(ClassEnrollment).filter(ClassEnrollment.class_id == test.class_id).all()
    
    # Get existing results
    existing_results = {r.student_id: r for r in TestResult.query.filter_by(test_id=test_id).all()}
    
    return render_template('teacher/grade_test.html', test=test, students=students, existing_results=existing_results)

@teacher_bp.route('/attendance')
@login_required
@teacher_required
def attendance():
    teacher = Teacher.query.filter_by(user_id=current_user.id).first()
    subjects = Subject.query.filter_by(teacher_id=teacher.id).all()
    
    # Get recent attendance records
    recent_attendance = Attendance.query.join(Subject).filter(Subject.teacher_id == teacher.id).order_by(Attendance.date.desc()).limit(20).all()
    
    return render_template('teacher/attendance.html', subjects=subjects, recent_attendance=recent_attendance)

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required
from models import User, Student, Teacher, Class, Subject
from database import db
from utils import admin_required

admin_bp = Blueprint('admin', __name__)

@admin_bp.route('/dashboard')
@login_required
@admin_required
def dashboard():
    # Get statistics for dashboard
    stats = {
        'total_students': Student.query.filter_by(is_active=True).count(),
        'total_teachers': Teacher.query.count(),
        'total_classes': Class.query.count(),
        'total_subjects': Subject.query.count(),
        'total_users': User.query.count()
    }
    
    # Get recent registrations
    recent_students = Student.query.order_by(Student.registration_date.desc()).limit(5).all()
    recent_users = User.query.order_by(User.created_at.desc()).limit(5).all()
    
    return render_template('admin/dashboard.html', 
                         stats=stats, 
                         recent_students=recent_students,
                         recent_users=recent_users)

@admin_bp.route('/users')
@login_required
@admin_required
def users():
    page = request.args.get('page', 1, type=int)
    users = User.query.paginate(page=page, per_page=20, error_out=False)
    return render_template('admin/users.html', users=users)

@admin_bp.route('/users/create', methods=['GET', 'POST'])
@login_required
@admin_required
def create_user():
    if request.method == 'POST':
        full_name = request.form.get('full_name')
        email = request.form.get('email')
        password = request.form.get('password')
        role = request.form.get('role')
        
        # Validation
        if not all([full_name, email, password, role]):
            flash('Please fill in all fields', 'error')
            return render_template('admin/create_user.html')
        
        if User.query.filter_by(email=email).first():
            flash('Email already exists', 'error')
            return render_template('admin/create_user.html')
        
        # Create user
        user = User(full_name=full_name, email=email, role=role)
        user.set_password(password)
        
        try:
            db.session.add(user)
            db.session.commit()
            
            # If teacher, create teacher record
            if role == 'teacher':
                staff_id = request.form.get('staff_id')
                teacher = Teacher(user_id=user.id, staff_id=staff_id)
                db.session.add(teacher)
                db.session.commit()
            
            flash('User created successfully', 'success')
            return redirect(url_for('admin.users'))
            
        except Exception as e:
            db.session.rollback()
            flash('Error creating user', 'error')
    
    return render_template('admin/create_user.html')

@admin_bp.route('/students')
@login_required
@admin_required
def students():
    page = request.args.get('page', 1, type=int)
    students = Student.query.filter_by(is_active=True).paginate(page=page, per_page=20, error_out=False)
    return render_template('admin/students.html', students=students)

@admin_bp.route('/students/create', methods=['GET', 'POST'])
@login_required
@admin_required
def create_student():
    if request.method == 'POST':
        full_name = request.form.get('full_name')
        dob = request.form.get('dob')
        gender = request.form.get('gender')
        parent_id = request.form.get('parent_id')
        
        if not all([full_name, dob, gender]):
            flash('Please fill in required fields', 'error')
            return render_template('admin/create_student.html')
        
        # Convert date string to date object
        from datetime import datetime
        try:
            dob = datetime.strptime(dob, '%Y-%m-%d').date()
        except ValueError:
            flash('Invalid date format', 'error')
            return render_template('admin/create_student.html')
        
        student = Student(
            full_name=full_name,
            dob=dob,
            gender=gender,
            parent_id=parent_id if parent_id else None
        )
        
        try:
            db.session.add(student)
            db.session.commit()
            flash('Student created successfully', 'success')
            return redirect(url_for('admin.students'))
        except Exception as e:
            db.session.rollback()
            flash('Error creating student', 'error')
    
    # Get parents for dropdown
    parents = User.query.filter_by(role='parent').all()
    return render_template('admin/create_student.html', parents=parents)

@admin_bp.route('/classes')
@login_required
@admin_required
def classes():
    classes = Class.query.all()
    return render_template('admin/classes.html', classes=classes)

@admin_bp.route('/classes/create', methods=['GET', 'POST'])
@login_required
@admin_required
def create_class():
    if request.method == 'POST':
        name = request.form.get('name')
        academic_year = request.form.get('academic_year')
        
        if not all([name, academic_year]):
            flash('Please fill in all fields', 'error')
            return render_template('admin/create_class.html')
        
        class_obj = Class(name=name, academic_year=academic_year)
        
        try:
            db.session.add(class_obj)
            db.session.commit()
            flash('Class created successfully', 'success')
            return redirect(url_for('admin.classes'))
        except Exception as e:
            db.session.rollback()
            flash('Error creating class', 'error')
    
    return render_template('admin/create_class.html')

@admin_bp.route('/subjects')
@login_required
@admin_required
def subjects():
    subjects = Subject.query.all()
    return render_template('admin/subjects.html', subjects=subjects)

@admin_bp.route('/subjects/create', methods=['GET', 'POST'])
@login_required
@admin_required
def create_subject():
    if request.method == 'POST':
        name = request.form.get('name')
        description = request.form.get('description')
        teacher_id = request.form.get('teacher_id')
        
        if not name:
            flash('Subject name is required', 'error')
            return render_template('admin/create_subject.html')
        
        subject = Subject(
            name=name,
            description=description,
            teacher_id=teacher_id if teacher_id else None
        )
        
        try:
            db.session.add(subject)
            db.session.commit()
            flash('Subject created successfully', 'success')
            return redirect(url_for('admin.subjects'))
        except Exception as e:
            db.session.rollback()
            flash('Error creating subject', 'error')
    
    # Get teachers for dropdown
    teachers = Teacher.query.join(User).all()
    return render_template('admin/create_subject.html', teachers=teachers)

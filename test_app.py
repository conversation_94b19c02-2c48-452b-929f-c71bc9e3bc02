#!/usr/bin/env python3
"""
Basic test script for School Management System
Tests core functionality and database operations
"""

import os
import sys
import tempfile
import unittest
from datetime import datetime, date

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app
from database import db
from models import User, Student, Teacher, Class, Subject, Test, TestResult


class SchoolManagementTestCase(unittest.TestCase):
    
    def setUp(self):
        """Set up test database and application context"""
        self.db_fd, app.config['DATABASE'] = tempfile.mkstemp()
        app.config['TESTING'] = True
        app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
        app.config['WTF_CSRF_ENABLED'] = False
        
        self.app = app.test_client()
        self.app_context = app.app_context()
        self.app_context.push()
        
        db.create_all()
    
    def tearDown(self):
        """Clean up after tests"""
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
        os.close(self.db_fd)
        os.unlink(app.config['DATABASE'])
    
    def test_user_creation_and_authentication(self):
        """Test user creation and password authentication"""
        # Create a user
        user = User(
            full_name="Test Admin",
            email="<EMAIL>",
            role="admin"
        )
        user.set_password("testpass123")
        
        db.session.add(user)
        db.session.commit()
        
        # Test password verification
        self.assertTrue(user.check_password("testpass123"))
        self.assertFalse(user.check_password("wrongpass"))
        
        # Test user retrieval
        retrieved_user = User.query.filter_by(email="<EMAIL>").first()
        self.assertIsNotNone(retrieved_user)
        self.assertEqual(retrieved_user.full_name, "Test Admin")
        self.assertEqual(retrieved_user.role, "admin")
    
    def test_student_creation(self):
        """Test student creation and parent relationship"""
        # Create parent user
        parent = User(
            full_name="Test Parent",
            email="<EMAIL>",
            role="parent"
        )
        parent.set_password("parentpass")
        db.session.add(parent)
        db.session.commit()
        
        # Create student
        student = Student(
            full_name="Test Student",
            dob=date(2010, 5, 15),
            gender="Female",
            parent_id=parent.id
        )
        db.session.add(student)
        db.session.commit()
        
        # Test relationships
        self.assertEqual(student.parent.full_name, "Test Parent")
        self.assertEqual(len(parent.children), 1)
        self.assertEqual(parent.children[0].full_name, "Test Student")
    
    def test_teacher_and_subject_creation(self):
        """Test teacher creation and subject assignment"""
        # Create teacher user
        teacher_user = User(
            full_name="Test Teacher",
            email="<EMAIL>",
            role="teacher"
        )
        teacher_user.set_password("teacherpass")
        db.session.add(teacher_user)
        db.session.commit()
        
        # Create teacher profile
        teacher = Teacher(
            user_id=teacher_user.id,
            staff_id="T001"
        )
        db.session.add(teacher)
        db.session.commit()
        
        # Create subject
        subject = Subject(
            name="Mathematics",
            description="Basic mathematics course",
            teacher_id=teacher.id
        )
        db.session.add(subject)
        db.session.commit()
        
        # Test relationships
        self.assertEqual(teacher.user.full_name, "Test Teacher")
        self.assertEqual(len(teacher.subjects), 1)
        self.assertEqual(teacher.subjects[0].name, "Mathematics")
    
    def test_class_and_enrollment(self):
        """Test class creation and student enrollment"""
        # Create class
        class_obj = Class(
            name="Grade 9-A",
            academic_year="2023-2024"
        )
        db.session.add(class_obj)
        db.session.commit()
        
        # Create student
        student = Student(
            full_name="Test Student",
            dob=date(2010, 1, 1),
            gender="Male"
        )
        db.session.add(student)
        db.session.commit()
        
        # Test class creation
        self.assertEqual(class_obj.name, "Grade 9-A")
        self.assertEqual(class_obj.academic_year, "2023-2024")
    
    def test_test_creation_and_grading(self):
        """Test test creation and grade calculation"""
        # Create necessary objects
        teacher_user = User(full_name="Teacher", email="<EMAIL>", role="teacher")
        teacher_user.set_password("pass")
        db.session.add(teacher_user)
        db.session.commit()
        
        teacher = Teacher(user_id=teacher_user.id, staff_id="T001")
        db.session.add(teacher)
        db.session.commit()
        
        subject = Subject(name="Math", teacher_id=teacher.id)
        db.session.add(subject)
        db.session.commit()
        
        class_obj = Class(name="Grade 9", academic_year="2023")
        db.session.add(class_obj)
        db.session.commit()
        
        student = Student(full_name="Student", dob=date(2010, 1, 1))
        db.session.add(student)
        db.session.commit()
        
        # Create test
        test = Test(
            subject_id=subject.id,
            class_id=class_obj.id,
            title="Mid-term Exam",
            date=date.today(),
            total_score=100
        )
        
        # Set grade scale
        grade_scale = {"A": 90, "B": 80, "C": 70, "D": 60, "F": 0}
        test.set_grade_scale(grade_scale)
        
        db.session.add(test)
        db.session.commit()
        
        # Test grade calculation
        self.assertEqual(test.calculate_grade(95), "A")
        self.assertEqual(test.calculate_grade(85), "B")
        self.assertEqual(test.calculate_grade(75), "C")
        self.assertEqual(test.calculate_grade(65), "D")
        self.assertEqual(test.calculate_grade(45), "F")
        
        # Create test result
        result = TestResult(
            test_id=test.id,
            student_id=student.id,
            score=85,
            grade=test.calculate_grade(85)
        )
        db.session.add(result)
        db.session.commit()
        
        self.assertEqual(result.grade, "B")
    
    def test_application_routes(self):
        """Test basic application routes"""
        # Test home page redirect
        response = self.app.get('/')
        self.assertEqual(response.status_code, 302)  # Redirect to login
        
        # Test login page
        response = self.app.get('/auth/login')
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'School Management', response.data)
        
        # Test registration page
        response = self.app.get('/auth/register')
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'Setup Administrator', response.data)
    
    def test_user_registration_flow(self):
        """Test user registration through web interface"""
        # Test first user registration (admin)
        response = self.app.post('/auth/register', data={
            'full_name': 'Admin User',
            'email': '<EMAIL>',
            'password': 'admin123',
            'confirm_password': 'admin123'
        }, follow_redirects=True)
        
        self.assertEqual(response.status_code, 200)
        
        # Verify user was created
        user = User.query.filter_by(email='<EMAIL>').first()
        self.assertIsNotNone(user)
        self.assertEqual(user.role, 'admin')
        self.assertTrue(user.check_password('admin123'))


def run_tests():
    """Run all tests"""
    print("Running School Management System Tests...")
    print("=" * 50)
    
    # Create test suite
    loader = unittest.TestLoader()
    suite = loader.loadTestsFromTestCase(SchoolManagementTestCase)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    print("\n" + "=" * 50)
    if result.wasSuccessful():
        print("✅ All tests passed!")
    else:
        print("❌ Some tests failed!")
        print(f"Failures: {len(result.failures)}")
        print(f"Errors: {len(result.errors)}")
    
    return result.wasSuccessful()


if __name__ == '__main__':
    success = run_tests()
    sys.exit(0 if success else 1)
